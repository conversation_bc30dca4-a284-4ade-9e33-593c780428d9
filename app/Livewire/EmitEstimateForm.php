<?php

namespace App\Livewire;

use App\Helpers\Cotizaciones;
use App\Models\User;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Livewire\Component;

class EmitEstimateForm extends Component implements HasForms
{
    use InteractsWithForms;

    public array $data = [];

    public ?int $id = 0;

    public $quote;

    public function mount(int $id): void
    {
        $this->id = $id;

        $libreria = new Cotizaciones();

        $this->quote = $libreria->getRecord("Quotes", $id);

        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([]);
    }

    public function create(): void
    {
        $data = $this->form->getState();
    }

    public function render()
    {
        return view('livewire.emit-estimate-form');
    }
}
